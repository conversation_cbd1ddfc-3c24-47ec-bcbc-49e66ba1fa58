
import React from 'react';
import { RecordIcon } from './icons/RecordIcon.jsx';
import { StopIcon } from './icons/StopIcon.jsx';
import { ErrorIcon } from './icons/ErrorIcon.jsx';

/**
 * Controls component for recording and error simulation
 * @param {Object} props - Component props
 * @param {boolean} props.isRecording - Whether recording is active
 * @param {Function} props.onStartRecording - Start recording callback
 * @param {Function} props.onStopRecording - Stop recording callback
 * @param {Function} props.onSimulateJSError - Simulate JS error callback
 * @param {Function} props.onSimulateNetworkError - Simulate network error callback
 * @returns {JSX.Element} The controls component
 */
export const Controls = ({
  isRecording,
  onStartRecording,
  onStopRecording,
  onSimulateJSError,
  onSimulateNetworkError,
}) => {
  return (
    <div className="mb-4 p-3 bg-slate-700/50 rounded-lg shadow-md">
      <div className="flex flex-col space-y-2">
        {!isRecording ? (
          <button
            onClick={onStartRecording}
            className="flex items-center justify-center w-full px-4 py-2 bg-green-600 hover:bg-green-500 text-white font-medium rounded-md shadow-md transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-opacity-75"
          >
            <RecordIcon className="w-4 h-4 mr-2" />
            Start Recording
          </button>
        ) : (
          <button
            onClick={onStopRecording}
            className="flex items-center justify-center w-full px-4 py-2 bg-red-600 hover:bg-red-500 text-white font-medium rounded-md shadow-md transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-opacity-75"
          >
            <StopIcon className="w-4 h-4 mr-2" />
            Stop Recording
            <span className="ml-2 w-2 h-2 bg-white rounded-full animate-ping"></span>
          </button>
        )}
        <div className="flex space-x-2">
          <button
            onClick={onSimulateJSError}
            disabled={isRecording}
            className="flex items-center justify-center flex-1 px-3 py-2 bg-yellow-500 hover:bg-yellow-400 text-slate-900 font-medium rounded-md shadow-md transition-colors duration-150 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-yellow-300 focus:ring-opacity-75 text-sm"
          >
            <ErrorIcon className="w-4 h-4 mr-1" />
            JS Error
          </button>
          <button
            onClick={onSimulateNetworkError}
            disabled={isRecording}
            className="flex items-center justify-center flex-1 px-3 py-2 bg-orange-500 hover:bg-orange-400 text-slate-900 font-medium rounded-md shadow-md transition-colors duration-150 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-orange-300 focus:ring-opacity-75 text-sm"
          >
            <ErrorIcon className="w-4 h-4 mr-1" />
            Network Error
          </button>
        </div>
      </div>
      {isRecording && <p className="text-center text-xs text-green-400 mt-2 animate-pulse">Recording active...</p>}
    </div>
  );
};
    