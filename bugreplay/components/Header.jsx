
import React from 'react';
import { BugIcon } from './icons/BugIcon.jsx';

/**
 * Header component for the BugReplay extension
 * @returns {JSX.Element} The header component
 */
export const Header = () => {
  return (
    <header className="w-full text-center p-4 border-b border-slate-700">
      <div className="flex items-center justify-center space-x-2 mb-1">
        <BugIcon className="w-8 h-8 text-indigo-400" />
        <h1 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 to-purple-500">
          BugReplay
        </h1>
      </div>
      <p className="text-slate-400 text-sm">"Catch Bugs the Right Way"</p>
    </header>
  );
};
    