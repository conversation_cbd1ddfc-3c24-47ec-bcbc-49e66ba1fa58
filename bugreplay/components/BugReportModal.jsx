
import React, { useState } from 'react';
import { GitHubIcon } from './icons/GitHubIcon.jsx';

/**
 * BugReportModal component for creating and editing bug reports
 * @param {Object} props - Component props
 * @param {Object} props.report - Initial bug report data
 * @param {Function} props.onClose - Close modal callback
 * @param {Function} props.onSubmit - Submit report callback
 * @returns {JSX.Element} The bug report modal component
 */
export const BugReportModal = ({ report: initialReport, onClose, onSubmit }) => {
  const [report, setReport] = useState(initialReport);

  /**
   * Handle form input changes
   * @param {Event} e - Input change event
   */
  const handleChange = (e) => {
    const { name, value } = e.target;
    setReport(prevReport => ({
      ...prevReport,
      [name]: value,
    }));
  };

  /**
   * Handle form submission
   * @param {Event} e - Form submit event
   */
  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(report);
  };

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50 transition-opacity duration-300 ease-in-out opacity-100">
      <div className="bg-slate-800 p-6 sm:p-8 rounded-lg shadow-2xl w-full max-w-2xl max-h-[90vh] md:max-h-[85vh] overflow-y-auto transform scale-100 transition-transform duration-300 ease-in-out">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-semibold text-indigo-400 flex items-center">
            <GitHubIcon className="w-7 h-7 mr-3 text-slate-300" />
            Create Bug Report
          </h2>
          <button
            onClick={onClose}
            className="text-slate-400 hover:text-slate-200 transition-colors text-2xl leading-none"
            aria-label="Close modal"
          >
            &times;
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-5">
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-slate-300 mb-1">Title</label>
            <input
              type="text"
              name="title"
              id="title"
              value={report.title}
              onChange={handleChange}
              className="w-full p-2.5 bg-slate-700 border border-slate-600 rounded-md text-slate-100 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 outline-none"
            />
          </div>

          <div>
            <label htmlFor="stepsToReproduce" className="block text-sm font-medium text-slate-300 mb-1">Steps to Reproduce</label>
            <textarea
              name="stepsToReproduce"
              id="stepsToReproduce"
              value={report.stepsToReproduce}
              onChange={handleChange}
              rows={4}
              className="w-full p-2.5 bg-slate-700 border border-slate-600 rounded-md text-slate-100 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 outline-none"
            />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="expectedResult" className="block text-sm font-medium text-slate-300 mb-1">Expected Result</label>
              <textarea
                name="expectedResult"
                id="expectedResult"
                value={report.expectedResult}
                onChange={handleChange}
                rows={2}
                className="w-full p-2.5 bg-slate-700 border border-slate-600 rounded-md text-slate-100 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 outline-none"
              />
            </div>
            <div>
              <label htmlFor="actualResult" className="block text-sm font-medium text-slate-300 mb-1">Actual Result</label>
              <textarea
                name="actualResult"
                id="actualResult"
                value={report.actualResult}
                onChange={handleChange}
                rows={2}
                className="w-full p-2.5 bg-slate-700 border border-slate-600 rounded-md text-slate-100 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 outline-none"
              />
            </div>
          </div>


          <div>
            <label htmlFor="environment" className="block text-sm font-medium text-slate-300 mb-1">Environment</label>
            <textarea
              name="environment"
              id="environment"
              value={report.environment}
              onChange={handleChange}
              rows={3}
              className="w-full p-2.5 bg-slate-700 border border-slate-600 rounded-md text-slate-100 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 outline-none"
            />
          </div>
          
          {report.screenshotUrl && (
            <div>
              <p className="block text-sm font-medium text-slate-300 mb-1">Screenshot</p>
              <img src={report.screenshotUrl} alt="Error Screenshot" className="w-full max-h-60 object-contain border border-slate-600 rounded-md mb-2" />
              <p className="text-xs text-slate-400">Screenshot automatically attached.</p>
            </div>
          )}

          <div>
            <label htmlFor="logs" className="block text-sm font-medium text-slate-300 mb-1">Attached Logs</label>
            <textarea
              name="logs"
              id="logs"
              value={report.logs}
              readOnly 
              rows={6}
              className="w-full p-2.5 bg-slate-900 border border-slate-700 rounded-md text-slate-400 text-xs font-mono focus:outline-none"
            />
          </div>

          <div className="flex justify-end space-x-3 pt-3">
            <button
              type="button"
              onClick={onClose}
              className="px-5 py-2.5 bg-slate-600 hover:bg-slate-500 text-slate-100 rounded-md transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-5 py-2.5 bg-indigo-600 hover:bg-indigo-500 text-white font-semibold rounded-md transition-colors flex items-center"
            >
              <GitHubIcon className="w-5 h-5 mr-2" />
              Create Issue (Simulated)
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
