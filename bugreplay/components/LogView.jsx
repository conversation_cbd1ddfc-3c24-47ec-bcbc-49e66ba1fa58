
import React from 'react';
import { LogType } from '../types.js';
import { ConsoleIcon } from './icons/ConsoleIcon.jsx';
import { NetworkIcon } from './icons/NetworkIcon.jsx';
import { MouseClickIcon } from './icons/MouseClickIcon.jsx';
import { ErrorIcon } from './icons/ErrorIcon.jsx'; // Re-use for system/DOM logs or specific error icon
import { CogIcon } from './icons/CogIcon.jsx'; // For DOM mutations or System messages

/**
 * Get the appropriate icon for a log entry
 * @param {string} type - Log type
 * @param {string} [status] - Optional status
 * @returns {JSX.Element|null} The icon component
 */
const getLogIcon = (type, status) => {
  switch (type) {
    case LogType.USER_ACTION:
      return <MouseClickIcon className="w-4 h-4 text-blue-400" />;
    case LogType.CONSOLE_LOG:
      return <ConsoleIcon className="w-4 h-4 text-gray-400" />;
    case LogType.CONSOLE_ERROR:
      return <ErrorIcon className="w-4 h-4 text-red-400" />;
    case LogType.NETWORK_REQUEST:
      return <NetworkIcon className={`w-4 h-4 ${status === 'error' ? 'text-red-400' : 'text-green-400'}`} />;
    case LogType.DOM_MUTATION:
      return <CogIcon className="w-4 h-4 text-purple-400" />; // Example icon
    case LogType.SYSTEM:
      return <CogIcon className="w-4 h-4 text-teal-400" />; // Example icon
    default:
      return null;
  }
};

/**
 * Get the appropriate color class for a log entry
 * @param {string} type - Log type
 * @param {string} [status] - Optional status
 * @returns {string} CSS class string
 */
const getLogColor = (type, status) => {
  switch (type) {
    case LogType.USER_ACTION:
      return 'text-blue-300';
    case LogType.CONSOLE_LOG:
      return 'text-slate-300';
    case LogType.CONSOLE_ERROR:
      return 'text-red-300 font-semibold';
    case LogType.NETWORK_REQUEST:
      return status === 'error' ? 'text-red-300 font-semibold' : 'text-green-300';
    case LogType.DOM_MUTATION:
      return 'text-purple-300';
    case LogType.SYSTEM:
      return 'text-teal-300 italic';
    default:
      return 'text-slate-300';
  }
};

/**
 * LogView component for displaying log entries
 * @param {Object} props - Component props
 * @param {Array} props.logs - Array of log entries
 * @returns {JSX.Element} The log view component
 */
export const LogView = ({ logs }) => {
  if (logs.length === 0) {
    return (
      <div className="mt-6 p-10 text-center text-slate-500 bg-slate-700/30 rounded-lg">
        No logs recorded yet. Start recording to see events here.
      </div>
    );
  }

  return (
    <div className="mt-6">
      <h3 className="text-xl font-semibold mb-3 text-slate-300">Event Log</h3>
      <div className="h-96 overflow-y-auto bg-slate-900 p-4 rounded-lg shadow-inner border border-slate-700 space-y-2">
        {logs.map((log) => (
          <div
            key={log.id}
            className={`flex items-start p-2.5 rounded-md text-sm ${getLogColor(log.type, log.status)} bg-slate-800/50 hover:bg-slate-700/70 transition-colors`}
          >
            <span className="mr-2 mt-0.5 shrink-0">{getLogIcon(log.type, log.status)}</span>
            <span className="mr-3 font-mono text-xs text-slate-500">{log.timestamp.toLocaleTimeString()}</span>
            <span className="flex-grow break-words">{log.message}
              {log.status && log.type === LogType.NETWORK_REQUEST && (
                <span className={`ml-2 px-1.5 py-0.5 text-xs rounded ${log.status === 'error' ? 'bg-red-500/30 text-red-200' : 'bg-green-500/30 text-green-200'}`}>
                  {log.status}
                </span>
              )}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};