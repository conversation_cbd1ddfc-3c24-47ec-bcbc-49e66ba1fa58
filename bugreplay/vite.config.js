import path from 'path';
import { fileURLToPath } from 'url';
import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import { copyFileSync, existsSync, mkdirSync, readFileSync, writeFileSync, readdirSync } from 'fs';
import { visualizer } from 'rollup-plugin-visualizer';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

export default defineConfig(({ mode }) => {
    const env = loadEnv(mode, '.', '');
    const isDev = mode === 'development';

    return {
      base: './',
      plugins: [
        react(),
        // Custom plugin to copy static files and manifest
        {
          name: 'copy-extension-files',
          writeBundle() {
            // Ensure directories exist
            mkdirSync('dist/icons', { recursive: true });

            // Copy manifest
            if (existsSync('manifest.json')) {
              copyFileSync('manifest.json', 'dist/manifest.json');
            }

            // Copy icons
            const iconSizes = ['16', '48', '128'];
            iconSizes.forEach(size => {
              const src = `icons/icon${size}.png`;
              const dest = `dist/icons/icon${size}.png`;
              if (existsSync(src)) {
                copyFileSync(src, dest);
              }
            });

            // Fix HTML file to include the popup script
            const htmlPath = 'dist/index.html';
            if (existsSync(htmlPath)) {
              let htmlContent = readFileSync(htmlPath, 'utf8');
              // Find the popup JS file
              const assetsDir = 'dist/assets';
              if (existsSync(assetsDir)) {
                const files = readdirSync(assetsDir);
                const popupFile = files.find(file => file.startsWith('popup-') && file.endsWith('.js'));
                if (popupFile) {
                  // Only inject if not already present
                  if (!htmlContent.includes(`src="./assets/${popupFile}"`)) {
                    // Inject the script tag before closing body
                    htmlContent = htmlContent.replace(
                      '</body>',
                      `    <script type="module" src="./assets/${popupFile}"></script>\n  </body>`
                    );
                    writeFileSync(htmlPath, htmlContent);
                  }
                }
              }
            }
          }
        },
        // Bundle analyzer (only in production)
        !isDev && visualizer({
          filename: 'dist/bundle-analysis.html',
          open: false,
          gzipSize: true,
          brotliSize: true,
        }),
      ].filter(Boolean),

      css: {
        postcss: './postcss.config.js',
      },

      define: {
        'process.env.API_KEY': JSON.stringify(env.GEMINI_API_KEY),
        'process.env.GEMINI_API_KEY': JSON.stringify(env.GEMINI_API_KEY),
        'process.env.NODE_ENV': JSON.stringify(mode)
      },

      resolve: {
        alias: {
          '@': path.resolve(__dirname, '.'),
        }
      },

      build: {
        outDir: 'dist',
        sourcemap: isDev ? 'inline' : true,
        minify: isDev ? false : 'esbuild',
        target: 'esnext',
        modulePreload: false,
        // Disable CSS code splitting for Chrome extensions
        cssCodeSplit: false,
        // Enhanced optimization
        rollupOptions: {
          input: {
            popup: path.resolve(__dirname, 'index.html'),
            background: path.resolve(__dirname, 'background.js'),
            content: path.resolve(__dirname, 'content.js'),
          },
          output: {
            entryFileNames: (chunk) => {
              // Keep background and content scripts in root for Chrome extension
              if (chunk.name === 'background' || chunk.name === 'content') {
                return '[name].js';
              }
              return 'assets/[name]-[hash].js';
            },
            chunkFileNames: 'assets/[name]-[hash].js',
            assetFileNames: (assetInfo) => {
              // Keep HTML files in root
              if (assetInfo.name?.endsWith('.html')) {
                return '[name].[ext]';
              }
              return 'assets/[name]-[hash].[ext]';
            },
          },
        },
        // Additional optimizations
        assetsInlineLimit: 4096,
        chunkSizeWarningLimit: 1000,
        reportCompressedSize: !isDev,
      },

      server: {
        port: 3000,
        hmr: {
          port: 3001
        }
      }
    };
});
