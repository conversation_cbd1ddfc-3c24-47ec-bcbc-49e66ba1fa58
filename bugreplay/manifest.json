{"manifest_version": 3, "name": "BugReplay - <PERSON> Bugs the Right Way", "version": "1.0.0", "description": "A browser extension that records user actions, DOM changes, network logs, and console errors.", "permissions": ["storage", "activeTab", "scripting", "tabs"], "action": {"default_popup": "index.html", "default_icon": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content.js"], "run_at": "document_idle"}], "icons": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "web_accessible_resources": [{"resources": ["icons/*.png"], "matches": ["<all_urls>"]}]}